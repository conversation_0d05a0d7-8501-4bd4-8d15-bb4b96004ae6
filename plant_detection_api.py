#!/usr/bin/env python3
"""
Plant Detection API Backend
Handles model loading, inference, and plant information retrieval
"""

import os
import json
import yaml
import cv2
import numpy as np
from PIL import Image
import io
import base64
from ultralytics import YOLO
from typing import Dict, List, Optional, Tuple

class PlantDetectionAPI:
    def __init__(self, model_path: Optional[str] = None):
        """Initialize the Plant Detection API"""
        self.model = None
        self.plant_db = None
        self.class_names = []
        
        # Load model
        if model_path:
            self.load_model(model_path)
        else:
            self.auto_load_model()
        
        # Load plant database
        self.load_plant_database()
        
        # Load class names
        self.load_class_names()
    
    def auto_load_model(self) -> bool:
        """Automatically find and load the best available model"""
        model_paths = [
            "runs/detect/enhanced_plant_detection/weights/best.pt",
            "runs/detect/train/weights/best.pt",
            "runs/detect/train2/weights/best.pt",
            "yolov8m.pt",
            "yolov8s.pt",
            "yolov8n.pt"
        ]
        
        for path in model_paths:
            if self.load_model(path):
                return True
        
        print("❌ No model could be loaded")
        return False
    
    def load_model(self, model_path: str) -> bool:
        """Load YOLO model from specified path"""
        try:
            if os.path.exists(model_path) or model_path.startswith('yolov8'):
                self.model = YOLO(model_path)
                print(f"✓ Model loaded from: {model_path}")
                return True
        except Exception as e:
            print(f"❌ Failed to load model from {model_path}: {e}")
        return False
    
    def load_plant_database(self) -> bool:
        """Load plant information database"""
        try:
            with open('plant_database.json', 'r', encoding='utf-8') as f:
                self.plant_db = json.load(f)
            print("✓ Plant database loaded successfully")
            return True
        except FileNotFoundError:
            print("❌ Plant database not found")
            return False
        except Exception as e:
            print(f"❌ Error loading plant database: {e}")
            return False
    
    def load_class_names(self) -> bool:
        """Load class names from data configuration"""
        config_files = ['enhanced_data.yaml', 'data.yaml']
        
        for config_file in config_files:
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    if 'names' in config:
                        self.class_names = config['names']
                        print(f"✓ Class names loaded from {config_file}")
                        return True
            except FileNotFoundError:
                continue
            except Exception as e:
                print(f"❌ Error loading {config_file}: {e}")
        
        print("⚠️ No class names configuration found")
        return False
    
    def get_plant_info(self, class_id: int) -> Dict:
        """Get detailed plant information by class ID"""
        if self.plant_db and str(class_id) in self.plant_db['plant_info']:
            return self.plant_db['plant_info'][str(class_id)]
        elif class_id < len(self.class_names):
            return {
                'common_name': self.class_names[class_id],
                'scientific_name': 'Unknown',
                'local_names': [],
                'family': 'Unknown',
                'description': 'No detailed information available'
            }
        else:
            return {
                'common_name': f'Unknown Plant (Class {class_id})',
                'scientific_name': 'Unknown',
                'local_names': [],
                'family': 'Unknown',
                'description': 'Plant not in database'
            }
    
    def preprocess_image(self, image_data: bytes) -> np.ndarray:
        """Preprocess image for inference"""
        try:
            # Convert bytes to PIL Image
            image = Image.open(io.BytesIO(image_data))
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Convert to numpy array
            image_array = np.array(image)
            
            return image_array
        except Exception as e:
            raise ValueError(f"Error preprocessing image: {e}")
    
    def detect_plants(self, image_data: bytes, confidence_threshold: float = 0.25) -> List[Dict]:
        """Detect plants in image and return results with plant information"""
        if self.model is None:
            raise RuntimeError("Model not loaded")
        
        try:
            # Preprocess image
            image_array = self.preprocess_image(image_data)
            
            # Run inference
            results = self.model.predict(image_array, conf=confidence_threshold, verbose=False)
            
            # Process results
            detections = []
            for result in results:
                if result.boxes is not None:
                    for box in result.boxes:
                        class_id = int(box.cls.item())
                        confidence = float(box.conf.item())
                        bbox = box.xyxy.tolist()[0] if len(box.xyxy) > 0 else []
                        
                        # Get plant information
                        plant_info = self.get_plant_info(class_id)
                        
                        detection = {
                            'class_id': class_id,
                            'confidence': confidence,
                            'bbox': bbox,  # [x1, y1, x2, y2]
                            'plant_info': plant_info
                        }
                        detections.append(detection)
            
            return detections
            
        except Exception as e:
            raise RuntimeError(f"Error during plant detection: {e}")
    
    def detect_from_file(self, file_path: str, confidence_threshold: float = 0.25) -> List[Dict]:
        """Detect plants from image file"""
        try:
            with open(file_path, 'rb') as f:
                image_data = f.read()
            return self.detect_plants(image_data, confidence_threshold)
        except Exception as e:
            raise RuntimeError(f"Error reading file {file_path}: {e}")
    
    def detect_from_base64(self, base64_data: str, confidence_threshold: float = 0.25) -> List[Dict]:
        """Detect plants from base64 encoded image"""
        try:
            # Remove data URL prefix if present
            if base64_data.startswith('data:image'):
                base64_data = base64_data.split(',')[1]
            
            image_data = base64.b64decode(base64_data)
            return self.detect_plants(image_data, confidence_threshold)
        except Exception as e:
            raise RuntimeError(f"Error decoding base64 image: {e}")
    
    def draw_detections(self, image_data: bytes, detections: List[Dict]) -> bytes:
        """Draw detection boxes and labels on image"""
        try:
            # Load image
            image = Image.open(io.BytesIO(image_data))
            image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # Draw detections
            for detection in detections:
                bbox = detection['bbox']
                if len(bbox) == 4:
                    x1, y1, x2, y2 = map(int, bbox)
                    
                    # Draw bounding box
                    cv2.rectangle(image_cv, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    
                    # Prepare label
                    plant_info = detection['plant_info']
                    confidence = detection['confidence']
                    label = f"{plant_info['common_name']} ({confidence:.2f})"
                    
                    # Draw label background
                    label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                    cv2.rectangle(image_cv, (x1, y1 - label_size[1] - 10), 
                                (x1 + label_size[0], y1), (0, 255, 0), -1)
                    
                    # Draw label text
                    cv2.putText(image_cv, label, (x1, y1 - 5), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
            
            # Convert back to bytes
            image_rgb = cv2.cvtColor(image_cv, cv2.COLOR_BGR2RGB)
            image_pil = Image.fromarray(image_rgb)
            
            output_buffer = io.BytesIO()
            image_pil.save(output_buffer, format='JPEG', quality=95)
            return output_buffer.getvalue()
            
        except Exception as e:
            raise RuntimeError(f"Error drawing detections: {e}")
    
    def get_model_info(self) -> Dict:
        """Get information about the loaded model"""
        if self.model is None:
            return {'status': 'No model loaded'}
        
        return {
            'status': 'Model loaded',
            'model_type': str(type(self.model)),
            'num_classes': len(self.class_names),
            'class_names': self.class_names,
            'database_loaded': self.plant_db is not None
        }
    
    def batch_detect(self, image_paths: List[str], confidence_threshold: float = 0.25) -> Dict[str, List[Dict]]:
        """Detect plants in multiple images"""
        results = {}
        
        for image_path in image_paths:
            try:
                detections = self.detect_from_file(image_path, confidence_threshold)
                results[image_path] = detections
            except Exception as e:
                results[image_path] = {'error': str(e)}
        
        return results

# Example usage and testing
if __name__ == "__main__":
    print("🌱 Plant Detection API Test")
    print("=" * 40)
    
    # Initialize API
    api = PlantDetectionAPI()
    
    # Print model info
    info = api.get_model_info()
    print(f"Model Status: {info['status']}")
    print(f"Number of Classes: {info.get('num_classes', 'Unknown')}")
    print(f"Database Loaded: {info.get('database_loaded', False)}")
    
    # Test with sample image if available
    test_images = ['test/images']
    if os.path.exists('test/images'):
        sample_images = [f for f in os.listdir('test/images') 
                        if f.lower().endswith(('.jpg', '.jpeg', '.png'))][:3]
        
        for img_name in sample_images:
            img_path = os.path.join('test/images', img_name)
            try:
                detections = api.detect_from_file(img_path)
                print(f"\n📸 {img_name}: {len(detections)} plants detected")
                
                for i, detection in enumerate(detections[:2]):  # Show first 2
                    plant = detection['plant_info']
                    conf = detection['confidence']
                    print(f"  {i+1}. {plant['common_name']} ({plant['scientific_name']}) - {conf:.2f}")
                    
            except Exception as e:
                print(f"❌ Error processing {img_name}: {e}")
    
    print("\n✓ API test completed")
