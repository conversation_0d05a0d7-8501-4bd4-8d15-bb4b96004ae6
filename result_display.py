#!/usr/bin/env python3
"""
Result Display System for Plant Detection
Handles formatting and displaying detection results with plant information
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Optional
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image
import numpy as np

class PlantDetectionResultDisplay:
    def __init__(self, plant_database_path: str = 'plant_database.json'):
        """Initialize the result display system"""
        self.plant_db = self.load_plant_database(plant_database_path)
        self.results_history = []
    
    def load_plant_database(self, database_path: str) -> Optional[Dict]:
        """Load plant information database"""
        try:
            with open(database_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"❌ Plant database not found at {database_path}")
            return None
        except Exception as e:
            print(f"❌ Error loading plant database: {e}")
            return None
    
    def format_detection_result(self, detection: Dict) -> Dict:
        """Format a single detection result for display"""
        plant_info = detection.get('plant_info', {})
        
        formatted_result = {
            'detection_id': detection.get('class_id', -1),
            'confidence': detection.get('confidence', 0.0),
            'confidence_percentage': f"{detection.get('confidence', 0.0) * 100:.1f}%",
            'bbox': detection.get('bbox', []),
            'plant_details': {
                'common_name': plant_info.get('common_name', 'Unknown'),
                'scientific_name': plant_info.get('scientific_name', 'Unknown'),
                'local_names': plant_info.get('local_names', []),
                'local_names_str': ', '.join(plant_info.get('local_names', [])),
                'family': plant_info.get('family', 'Unknown'),
                'description': plant_info.get('description', 'No description available')
            }
        }
        
        return formatted_result
    
    def format_detection_results(self, detections: List[Dict], image_filename: str = '') -> Dict:
        """Format multiple detection results for display"""
        formatted_detections = []
        
        for detection in detections:
            formatted_detections.append(self.format_detection_result(detection))
        
        result_summary = {
            'timestamp': datetime.now().isoformat(),
            'image_filename': image_filename,
            'total_detections': len(detections),
            'detections': formatted_detections,
            'unique_plants': len(set(d['plant_details']['common_name'] for d in formatted_detections)),
            'confidence_stats': self._calculate_confidence_stats(detections)
        }
        
        # Add to history
        self.results_history.append(result_summary)
        
        return result_summary
    
    def _calculate_confidence_stats(self, detections: List[Dict]) -> Dict:
        """Calculate confidence statistics"""
        if not detections:
            return {'min': 0, 'max': 0, 'average': 0}
        
        confidences = [d.get('confidence', 0) for d in detections]
        return {
            'min': min(confidences),
            'max': max(confidences),
            'average': sum(confidences) / len(confidences)
        }
    
    def generate_text_report(self, result_summary: Dict) -> str:
        """Generate a text report of detection results"""
        report = []
        report.append("🌱 PLANT DETECTION REPORT")
        report.append("=" * 50)
        report.append(f"📅 Timestamp: {result_summary['timestamp']}")
        report.append(f"📸 Image: {result_summary['image_filename']}")
        report.append(f"🔍 Total Detections: {result_summary['total_detections']}")
        report.append(f"🌿 Unique Plants: {result_summary['unique_plants']}")
        
        stats = result_summary['confidence_stats']
        report.append(f"📊 Confidence - Min: {stats['min']:.2f}, Max: {stats['max']:.2f}, Avg: {stats['average']:.2f}")
        report.append("")
        
        if result_summary['detections']:
            report.append("🌱 DETECTED PLANTS:")
            report.append("-" * 30)
            
            for i, detection in enumerate(result_summary['detections'], 1):
                plant = detection['plant_details']
                report.append(f"\n{i}. {plant['common_name']}")
                report.append(f"   🔬 Scientific Name: {plant['scientific_name']}")
                report.append(f"   🏷️  Family: {plant['family']}")
                report.append(f"   🌍 Local Names: {plant['local_names_str'] or 'None available'}")
                report.append(f"   📝 Description: {plant['description']}")
                report.append(f"   ✅ Confidence: {detection['confidence_percentage']}")
        else:
            report.append("❌ No plants detected in this image.")
        
        return "\n".join(report)
    
    def generate_html_report(self, result_summary: Dict) -> str:
        """Generate an HTML report of detection results"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Plant Detection Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
                .container {{ max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ text-align: center; color: #2c3e50; border-bottom: 2px solid #27ae60; padding-bottom: 20px; margin-bottom: 30px; }}
                .summary {{ background: #ecf0f1; padding: 20px; border-radius: 8px; margin-bottom: 30px; }}
                .detection {{ background: #f8f9fa; border-left: 4px solid #27ae60; padding: 20px; margin-bottom: 20px; border-radius: 5px; }}
                .plant-name {{ font-size: 1.3em; font-weight: bold; color: #2c3e50; margin-bottom: 10px; }}
                .scientific-name {{ font-style: italic; color: #7f8c8d; margin-bottom: 15px; }}
                .info-row {{ margin-bottom: 8px; }}
                .label {{ font-weight: bold; color: #34495e; }}
                .confidence {{ background: #27ae60; color: white; padding: 4px 12px; border-radius: 15px; font-size: 0.9em; }}
                .no-detections {{ text-align: center; color: #7f8c8d; font-size: 1.2em; padding: 40px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🌱 Plant Detection Report</h1>
                    <p>Automated Plant Identification Results</p>
                </div>
                
                <div class="summary">
                    <h3>📊 Summary</h3>
                    <div class="info-row"><span class="label">Timestamp:</span> {result_summary['timestamp']}</div>
                    <div class="info-row"><span class="label">Image:</span> {result_summary['image_filename']}</div>
                    <div class="info-row"><span class="label">Total Detections:</span> {result_summary['total_detections']}</div>
                    <div class="info-row"><span class="label">Unique Plants:</span> {result_summary['unique_plants']}</div>
                    <div class="info-row"><span class="label">Average Confidence:</span> {result_summary['confidence_stats']['average']:.2f}</div>
                </div>
        """
        
        if result_summary['detections']:
            html += "<h3>🌿 Detected Plants</h3>"
            
            for i, detection in enumerate(result_summary['detections'], 1):
                plant = detection['plant_details']
                html += f"""
                <div class="detection">
                    <div class="plant-name">{i}. {plant['common_name']}</div>
                    <div class="scientific-name">{plant['scientific_name']}</div>
                    
                    <div class="info-row">
                        <span class="label">Family:</span> {plant['family']}
                    </div>
                    
                    <div class="info-row">
                        <span class="label">Local Names:</span> {plant['local_names_str'] or 'None available'}
                    </div>
                    
                    <div class="info-row">
                        <span class="label">Description:</span> {plant['description']}
                    </div>
                    
                    <div class="info-row">
                        <span class="confidence">Confidence: {detection['confidence_percentage']}</span>
                    </div>
                </div>
                """
        else:
            html += '<div class="no-detections">❌ No plants detected in this image.</div>'
        
        html += """
            </div>
        </body>
        </html>
        """
        
        return html
    
    def save_report(self, result_summary: Dict, output_dir: str = 'reports', format: str = 'both') -> Dict[str, str]:
        """Save detection report to file(s)"""
        os.makedirs(output_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = f"plant_detection_report_{timestamp}"
        
        saved_files = {}
        
        if format in ['text', 'both']:
            text_report = self.generate_text_report(result_summary)
            text_path = os.path.join(output_dir, f"{base_filename}.txt")
            with open(text_path, 'w', encoding='utf-8') as f:
                f.write(text_report)
            saved_files['text'] = text_path
        
        if format in ['html', 'both']:
            html_report = self.generate_html_report(result_summary)
            html_path = os.path.join(output_dir, f"{base_filename}.html")
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_report)
            saved_files['html'] = html_path
        
        return saved_files
    
    def visualize_detections(self, image_path: str, detections: List[Dict], save_path: str = None) -> str:
        """Create a visualization of detections on the image"""
        try:
            # Load image
            image = Image.open(image_path)
            
            # Create figure and axis
            fig, ax = plt.subplots(1, figsize=(12, 8))
            ax.imshow(image)
            
            # Draw detections
            colors = plt.cm.Set3(np.linspace(0, 1, len(detections)))
            
            for detection, color in zip(detections, colors):
                bbox = detection.get('bbox', [])
                if len(bbox) == 4:
                    x1, y1, x2, y2 = bbox
                    width = x2 - x1
                    height = y2 - y1
                    
                    # Draw bounding box
                    rect = patches.Rectangle((x1, y1), width, height, 
                                           linewidth=2, edgecolor=color, facecolor='none')
                    ax.add_patch(rect)
                    
                    # Add label
                    plant_info = detection.get('plant_info', {})
                    label = f"{plant_info.get('common_name', 'Unknown')}\n{detection.get('confidence', 0):.2f}"
                    ax.text(x1, y1-10, label, fontsize=10, color='white', 
                           bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.8))
            
            ax.set_title('Plant Detection Results', fontsize=16, fontweight='bold')
            ax.axis('off')
            
            # Save or show
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                plt.close()
                return save_path
            else:
                plt.show()
                return "displayed"
                
        except Exception as e:
            print(f"❌ Error creating visualization: {e}")
            return ""
    
    def get_detection_statistics(self) -> Dict:
        """Get statistics from all detection history"""
        if not self.results_history:
            return {'message': 'No detection history available'}
        
        total_detections = sum(r['total_detections'] for r in self.results_history)
        total_images = len(self.results_history)
        
        all_plants = []
        for result in self.results_history:
            for detection in result['detections']:
                all_plants.append(detection['plant_details']['common_name'])
        
        unique_plants = list(set(all_plants))
        
        return {
            'total_images_processed': total_images,
            'total_detections': total_detections,
            'average_detections_per_image': total_detections / total_images if total_images > 0 else 0,
            'unique_plants_detected': len(unique_plants),
            'most_common_plants': self._get_most_common_plants(all_plants),
            'detection_history': len(self.results_history)
        }
    
    def _get_most_common_plants(self, plant_list: List[str], top_n: int = 5) -> List[Dict]:
        """Get the most commonly detected plants"""
        from collections import Counter
        
        plant_counts = Counter(plant_list)
        most_common = plant_counts.most_common(top_n)
        
        return [{'plant': plant, 'count': count} for plant, count in most_common]

# Example usage
if __name__ == "__main__":
    print("🌱 Plant Detection Result Display System Test")
    print("=" * 50)
    
    # Initialize display system
    display = PlantDetectionResultDisplay()
    
    # Example detection results
    sample_detections = [
        {
            'class_id': 0,
            'confidence': 0.85,
            'bbox': [100, 100, 200, 200],
            'plant_info': {
                'common_name': 'Adam Hawa ungu',
                'scientific_name': 'Ruellia simplex',
                'local_names': ['Purple Ruellia', 'Mexican Petunia'],
                'family': 'Acanthaceae',
                'description': 'A purple flowering ornamental plant'
            }
        }
    ]
    
    # Format results
    result_summary = display.format_detection_results(sample_detections, 'test_image.jpg')
    
    # Generate reports
    text_report = display.generate_text_report(result_summary)
    print("📄 Text Report Generated")
    print(text_report[:200] + "...")
    
    # Save reports
    saved_files = display.save_report(result_summary)
    print(f"\n💾 Reports saved: {saved_files}")
    
    print("\n✓ Result display system test completed")
