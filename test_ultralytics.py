#!/usr/bin/env python3
"""
Simple test script to check ultralytics installation
"""

try:
    from ultralytics import YOL<PERSON>
    print("✓ Ultralytics imported successfully")
    
    # Try to load a model
    model = YOLO("yolov8n.pt")  # Using nano version for faster download
    print("✓ YOLOv8 model loaded successfully")
    print(f"Model: {model}")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Installing ultralytics...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "ultralytics"])
    
except Exception as e:
    print(f"❌ Error: {e}")
