<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌱 Plant Detection System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .upload-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .upload-area {
            border: 3px dashed #4CAF50;
            border-radius: 15px;
            padding: 60px 20px;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            background: #e8f5e8;
            border-color: #45a049;
        }

        .upload-area.dragover {
            background: #e8f5e8;
            border-color: #45a049;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4em;
            color: #4CAF50;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 20px;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results-section {
            display: none;
        }

        .image-container {
            text-align: center;
            margin-bottom: 30px;
        }

        .uploaded-image {
            max-width: 100%;
            max-height: 500px;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .detections-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .detection-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .detection-card:hover {
            transform: translateY(-5px);
        }

        .plant-name {
            font-size: 1.4em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .scientific-name {
            font-style: italic;
            color: #7f8c8d;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .plant-info {
            margin-bottom: 10px;
        }

        .info-label {
            font-weight: bold;
            color: #34495e;
        }

        .info-value {
            color: #555;
        }

        .confidence {
            background: #4CAF50;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            display: inline-block;
            margin-top: 10px;
        }

        .no-detections {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
            font-size: 1.2em;
        }

        .error-message {
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }

        .new-upload-btn {
            background: #3498db;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 20px;
            font-size: 1em;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s ease;
        }

        .new-upload-btn:hover {
            background: #2980b9;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .detections-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌱 Plant Detection System</h1>
            <p>Upload an image to identify plants with scientific and local names</p>
        </div>

        <div class="main-content">
            <div class="upload-section" id="uploadSection">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📷</div>
                    <div class="upload-text">Drag and drop an image here or click to browse</div>
                    <input type="file" id="fileInput" class="file-input" accept="image/*">
                    <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                        Choose Image
                    </button>
                </div>
            </div>

            <div class="loading" id="loadingSection">
                <div class="spinner"></div>
                <p>Analyzing your image... Please wait</p>
            </div>

            <div class="results-section" id="resultsSection">
                <div class="image-container">
                    <img id="uploadedImage" class="uploaded-image" alt="Uploaded image">
                </div>
                
                <div id="detectionsContainer">
                    <!-- Detection results will be inserted here -->
                </div>
                
                <div style="text-align: center;">
                    <button class="new-upload-btn" onclick="resetUpload()">
                        Upload Another Image
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const uploadSection = document.getElementById('uploadSection');
        const loadingSection = document.getElementById('loadingSection');
        const resultsSection = document.getElementById('resultsSection');
        const uploadedImage = document.getElementById('uploadedImage');
        const detectionsContainer = document.getElementById('detectionsContainer');

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                showError('Please select a valid image file.');
                return;
            }

            if (file.size > 16 * 1024 * 1024) {
                showError('File size must be less than 16MB.');
                return;
            }

            uploadImage(file);
        }

        function uploadImage(file) {
            const formData = new FormData();
            formData.append('file', file);

            // Show loading
            uploadSection.style.display = 'none';
            loadingSection.style.display = 'block';
            resultsSection.style.display = 'none';

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                loadingSection.style.display = 'none';
                
                if (data.success) {
                    showResults(data);
                } else {
                    showError(data.error || 'An error occurred during processing.');
                }
            })
            .catch(error => {
                loadingSection.style.display = 'none';
                showError('Network error: ' + error.message);
            });
        }

        function showResults(data) {
            // Display uploaded image
            uploadedImage.src = 'data:image/jpeg;base64,' + data.image_data;
            
            // Display detections
            if (data.detections && data.detections.length > 0) {
                let detectionsHTML = '<div class="detections-grid">';
                
                data.detections.forEach((detection, index) => {
                    const plant = detection.plant_info;
                    const confidence = (detection.confidence * 100).toFixed(1);
                    
                    detectionsHTML += `
                        <div class="detection-card">
                            <div class="plant-name">${plant.common_name}</div>
                            <div class="scientific-name">${plant.scientific_name}</div>
                            
                            <div class="plant-info">
                                <span class="info-label">Family:</span>
                                <span class="info-value">${plant.family}</span>
                            </div>
                            
                            <div class="plant-info">
                                <span class="info-label">Local Names:</span>
                                <span class="info-value">${plant.local_names.join(', ') || 'None available'}</span>
                            </div>
                            
                            ${plant.description ? `
                                <div class="plant-info">
                                    <span class="info-label">Description:</span>
                                    <span class="info-value">${plant.description}</span>
                                </div>
                            ` : ''}
                            
                            <div class="confidence">Confidence: ${confidence}%</div>
                        </div>
                    `;
                });
                
                detectionsHTML += '</div>';
                detectionsContainer.innerHTML = detectionsHTML;
            } else {
                detectionsContainer.innerHTML = `
                    <div class="no-detections">
                        <p>No plants detected in this image.</p>
                        <p>Try uploading a clearer image or one with plants from our database.</p>
                    </div>
                `;
            }
            
            resultsSection.style.display = 'block';
        }

        function showError(message) {
            uploadSection.style.display = 'block';
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;
            uploadSection.appendChild(errorDiv);
            
            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }

        function resetUpload() {
            uploadSection.style.display = 'block';
            loadingSection.style.display = 'none';
            resultsSection.style.display = 'none';
            fileInput.value = '';
        }
    </script>
</body>
</html>
