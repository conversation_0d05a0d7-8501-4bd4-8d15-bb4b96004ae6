#!/usr/bin/env python3
"""
Enhanced YOLOv8 Plant Detection Training Script with Plant Information
"""

import os
import sys
import json
import yaml
from pathlib import Path

def check_ultralytics():
    """Check if ultralytics is installed and import YOLO"""
    try:
        from ultralytics import <PERSON><PERSON><PERSON>
        print("✓ Ultralytics imported successfully")
        return YOLO
    except ImportError:
        print("❌ Ultralytics not found. Installing...")
        os.system("pip install ultralytics")
        try:
            from ultralytics import Y<PERSON><PERSON>
            print("✓ Ultralytics installed and imported successfully")
            return YOLO
        except ImportError:
            print("❌ Failed to install ultralytics. Please install manually.")
            sys.exit(1)

def load_plant_database():
    """Load plant information database"""
    try:
        with open('plant_database.json', 'r', encoding='utf-8') as f:
            plant_db = json.load(f)
        print("✓ Plant database loaded successfully")
        return plant_db
    except FileNotFoundError:
        print("❌ Plant database not found")
        return None

def load_enhanced_config():
    """Load enhanced data configuration"""
    try:
        with open('enhanced_data.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print("✓ Enhanced data configuration loaded")
        return config
    except FileNotFoundError:
        print("❌ Enhanced data configuration not found, using default data.yaml")
        return None

class PlantDetectionModel:
    def __init__(self, model_name="yolov8m.pt"):
        self.YOLO = check_ultralytics()
        self.model = self.YOLO(model_name)
        self.plant_db = load_plant_database()
        self.config = load_enhanced_config()
        
    def get_plant_info(self, class_id):
        """Get detailed plant information by class ID"""
        if self.plant_db and str(class_id) in self.plant_db['plant_info']:
            return self.plant_db['plant_info'][str(class_id)]
        elif self.config and class_id < len(self.config['names']):
            # Fallback to basic info from config
            return {
                'common_name': self.config['names'][class_id],
                'scientific_name': 'Unknown',
                'local_names': [],
                'family': 'Unknown'
            }
        return None
    
    def train_model(self, epochs=2, data_config="enhanced_data.yaml"):
        """Train the model with enhanced configuration"""
        print(f"\n🔄 Starting training with {data_config} for {epochs} epochs...")
        
        # Check if enhanced config exists, fallback to regular data.yaml
        if not os.path.exists(data_config):
            print(f"⚠️ {data_config} not found, using data.yaml")
            data_config = "data.yaml"
        
        try:
            results = self.model.train(
                data=data_config,
                epochs=epochs,
                imgsz=640,
                batch=16,
                name="enhanced_plant_detection",
                save_period=1,  # Save every epoch
                patience=50,    # Early stopping patience
                plots=True      # Generate training plots
            )
            print("✓ Training completed successfully")
            return results
        except Exception as e:
            print(f"❌ Training failed: {e}")
            return None
    
    def load_best_model(self):
        """Load the best trained model"""
        best_model_paths = [
            "runs/detect/enhanced_plant_detection/weights/best.pt",
            "runs/detect/train/weights/best.pt",
            "runs/detect/train2/weights/best.pt"
        ]
        
        for path in best_model_paths:
            if os.path.exists(path):
                self.model = self.YOLO(path)
                print(f"✓ Loaded best model from: {path}")
                return True
        
        print("⚠️ No trained model found, using original model")
        return False
    
    def predict_with_info(self, source, save=True, save_txt=True, conf=0.25):
        """Run prediction and return results with plant information"""
        print(f"\n🔄 Running inference on: {source}")
        
        try:
            results = self.model.predict(
                source=source,
                save=save,
                save_txt=save_txt,
                conf=conf,
                name="enhanced_plant_inference"
            )
            
            # Process results to add plant information
            enhanced_results = []
            for result in results:
                if result.boxes is not None:
                    for box in result.boxes:
                        class_id = int(box.cls.item())
                        confidence = float(box.conf.item())
                        plant_info = self.get_plant_info(class_id)
                        
                        enhanced_result = {
                            'class_id': class_id,
                            'confidence': confidence,
                            'bbox': box.xyxy.tolist()[0] if len(box.xyxy) > 0 else [],
                            'plant_info': plant_info
                        }
                        enhanced_results.append(enhanced_result)
            
            print("✓ Inference completed successfully")
            return enhanced_results
            
        except Exception as e:
            print(f"❌ Inference failed: {e}")
            return []

def main():
    print("🌱 Enhanced Plant Detection System")
    print("=" * 50)
    
    # Initialize the model
    detector = PlantDetectionModel("yolov8m.pt")
    
    # Train the model
    print("\n📚 Training Phase")
    training_results = detector.train_model(epochs=2)
    
    if training_results:
        # Load the best trained model
        print("\n🔄 Loading best trained model...")
        detector.load_best_model()
        
        # Run inference on test images
        print("\n🔍 Inference Phase")
        test_images_path = "test/images"
        
        if os.path.exists(test_images_path):
            results = detector.predict_with_info(
                source=test_images_path,
                save=True,
                save_txt=True,
                conf=0.25
            )
            
            # Display some results
            print(f"\n📊 Detection Results Summary:")
            print(f"Total detections: {len(results)}")
            
            for i, result in enumerate(results[:5]):  # Show first 5 results
                plant_info = result['plant_info']
                if plant_info:
                    print(f"\nDetection {i+1}:")
                    print(f"  Common Name: {plant_info['common_name']}")
                    print(f"  Scientific Name: {plant_info['scientific_name']}")
                    print(f"  Local Names: {', '.join(plant_info['local_names'])}")
                    print(f"  Family: {plant_info['family']}")
                    print(f"  Confidence: {result['confidence']:.2f}")
        else:
            print(f"❌ Test images directory not found: {test_images_path}")
    
    print("\n🎉 Enhanced plant detection system setup completed!")
    print("📁 Check the 'runs/detect/' directory for training and inference results")

if __name__ == "__main__":
    main()
