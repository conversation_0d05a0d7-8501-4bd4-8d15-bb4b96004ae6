#!/usr/bin/env python3
"""
YOLOv8 Plant Detection Training and Inference Script
"""

import os
import sys
from pathlib import Path

def check_ultralytics():
    """Check if ultralytics is installed and import YOLO"""
    try:
        from ultralytics import <PERSON><PERSON><PERSON>
        print("✓ Ultralytics imported successfully")
        return YOLO
    except ImportError:
        print("❌ Ultralytics not found. Installing...")
        os.system("pip install ultralytics")
        try:
            from ultralytics import <PERSON><PERSON><PERSON>
            print("✓ Ultralytics installed and imported successfully")
            return YOLO
        except ImportError:
            print("❌ Failed to install ultralytics. Please install manually.")
            sys.exit(1)

def main():
    # Check and import YOLO
    YOLO = check_ultralytics()
    
    # Step 1: Load YOLOv8 model
    print("\n🔄 Loading YOLOv8 model...")
    model = YOLO("yolov8m.pt")  # You can change this to yolov8n.pt, yolov8s.pt, yolov8l.pt, yolov8x.pt
    print("✓ Model loaded successfully")
    
    # Step 2: Train the model
    print("\n🔄 Starting training...")
    try:
        # Update data.yaml path to use absolute path
        data_path = "data.yaml"
        if not os.path.exists(data_path):
            print(f"❌ Data file not found: {data_path}")
            return
            
        # Train the model
        results = model.train(
            data=data_path,
            epochs=2,  # You mentioned 2 epochs
            imgsz=640,
            batch=16,
            name="plant_detection_run"
        )
        print("✓ Training completed successfully")
        
        # Step 3: Load the best trained model for inference
        print("\n🔄 Loading trained model for inference...")
        # The trained model will be saved in runs/detect/plant_detection_run/weights/best.pt
        best_model_path = "runs/detect/plant_detection_run/weights/best.pt"
        
        if os.path.exists(best_model_path):
            infer_model = YOLO(best_model_path)
            print("✓ Best trained model loaded successfully")
        else:
            print("⚠️ Best model not found, using original model for inference")
            infer_model = model
        
        # Step 4: Run inference on test images
        print("\n🔄 Running inference on test images...")
        test_images_path = "test/images"
        
        if os.path.exists(test_images_path):
            results = infer_model.predict(
                test_images_path,
                save=True,
                save_txt=True,
                conf=0.25,  # Confidence threshold
                name="plant_detection_inference"
            )
            print("✓ Inference completed successfully")
            print(f"📁 Results saved in: runs/detect/plant_detection_inference/")
        else:
            print(f"❌ Test images directory not found: {test_images_path}")
            
    except Exception as e:
        print(f"❌ Error during training/inference: {str(e)}")
        return
    
    print("\n🎉 All tasks completed successfully!")
    print("📊 Check the 'runs/detect/' directory for training and inference results")

if __name__ == "__main__":
    main()
