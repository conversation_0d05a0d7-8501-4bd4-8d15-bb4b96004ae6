#!/usr/bin/env python3
"""
Flask Web Application for Plant Detection
"""

import os
import json
import yaml
from flask import Flask, render_template, request, jsonify, send_file
from werkzeug.utils import secure_filename
from PIL import Image
import io
import base64
from ultralytics import YOLO
import cv2
import numpy as np

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Global variables for model and plant database
model = None
plant_db = None

def load_model():
    """Load the trained YOLO model"""
    global model
    
    # Try to load the best trained model first
    model_paths = [
        "runs/detect/enhanced_plant_detection/weights/best.pt",
        "runs/detect/train/weights/best.pt",
        "yolov8m.pt"  # Fallback to pretrained model
    ]
    
    for path in model_paths:
        if os.path.exists(path):
            try:
                model = YOLO(path)
                print(f"✓ Model loaded from: {path}")
                return True
            except Exception as e:
                print(f"❌ Failed to load model from {path}: {e}")
                continue
    
    print("❌ No model could be loaded")
    return False

def load_plant_database():
    """Load plant information database"""
    global plant_db
    
    try:
        with open('plant_database.json', 'r', encoding='utf-8') as f:
            plant_db = json.load(f)
        print("✓ Plant database loaded successfully")
        return True
    except FileNotFoundError:
        print("❌ Plant database not found")
        return False

def get_plant_info(class_id):
    """Get detailed plant information by class ID"""
    if plant_db and str(class_id) in plant_db['plant_info']:
        return plant_db['plant_info'][str(class_id)]
    return {
        'common_name': f'Unknown Plant (Class {class_id})',
        'scientific_name': 'Unknown',
        'local_names': [],
        'family': 'Unknown',
        'description': 'No information available'
    }

def allowed_file(filename):
    """Check if file extension is allowed"""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and plant detection"""
    if 'file' not in request.files:
        return jsonify({'error': 'No file uploaded'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if not allowed_file(file.filename):
        return jsonify({'error': 'Invalid file type'}), 400
    
    try:
        # Save uploaded file
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Run plant detection
        if model is None:
            return jsonify({'error': 'Model not loaded'}), 500
        
        results = model.predict(filepath, conf=0.25)
        
        # Process results
        detections = []
        for result in results:
            if result.boxes is not None:
                for box in result.boxes:
                    class_id = int(box.cls.item())
                    confidence = float(box.conf.item())
                    bbox = box.xyxy.tolist()[0] if len(box.xyxy) > 0 else []
                    
                    plant_info = get_plant_info(class_id)
                    
                    detection = {
                        'class_id': class_id,
                        'confidence': confidence,
                        'bbox': bbox,
                        'plant_info': plant_info
                    }
                    detections.append(detection)
        
        # Convert image to base64 for display
        with open(filepath, 'rb') as img_file:
            img_data = base64.b64encode(img_file.read()).decode('utf-8')
        
        # Clean up uploaded file
        os.remove(filepath)
        
        return jsonify({
            'success': True,
            'detections': detections,
            'image_data': img_data,
            'filename': filename
        })
        
    except Exception as e:
        return jsonify({'error': f'Processing failed: {str(e)}'}), 500

@app.route('/plant_info/<int:class_id>')
def plant_info(class_id):
    """Get detailed plant information"""
    info = get_plant_info(class_id)
    return jsonify(info)

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'model_loaded': model is not None,
        'database_loaded': plant_db is not None
    })

if __name__ == '__main__':
    print("🌱 Starting Plant Detection Web Application")
    print("=" * 50)
    
    # Load model and database
    model_loaded = load_model()
    db_loaded = load_plant_database()
    
    if not model_loaded:
        print("⚠️ Warning: No model loaded. Please train a model first.")
    
    if not db_loaded:
        print("⚠️ Warning: Plant database not loaded.")
    
    print("\n🚀 Starting web server...")
    print("📱 Open your browser and go to: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
