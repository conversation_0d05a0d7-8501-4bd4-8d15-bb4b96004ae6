#!/usr/bin/env python3
"""
Complete Plant Detection System Deployment Script
Tests and deploys the entire plant detection workflow
"""

import os
import sys
import subprocess
import json
import time
from pathlib import Path

class PlantDetectionSystemDeployer:
    def __init__(self):
        self.project_root = Path.cwd()
        self.required_files = [
            'plant_database.json',
            'enhanced_data.yaml',
            'enhanced_training_script.py',
            'app.py',
            'plant_detection_api.py',
            'result_display.py',
            'templates/index.html'
        ]
        self.required_dirs = [
            'train/images',
            'train/labels',
            'valid/images',
            'valid/labels',
            'test/images',
            'test/labels'
        ]
    
    def check_system_requirements(self):
        """Check if all required files and directories exist"""
        print("🔍 Checking system requirements...")
        
        missing_files = []
        missing_dirs = []
        
        for file_path in self.required_files:
            if not (self.project_root / file_path).exists():
                missing_files.append(file_path)
        
        for dir_path in self.required_dirs:
            if not (self.project_root / dir_path).exists():
                missing_dirs.append(dir_path)
        
        if missing_files:
            print("❌ Missing required files:")
            for file in missing_files:
                print(f"   - {file}")
        
        if missing_dirs:
            print("❌ Missing required directories:")
            for dir in missing_dirs:
                print(f"   - {dir}")
        
        if not missing_files and not missing_dirs:
            print("✅ All required files and directories found")
            return True
        
        return False
    
    def install_dependencies(self):
        """Install required Python packages"""
        print("\n📦 Installing dependencies...")
        
        dependencies = [
            'ultralytics',
            'flask',
            'pillow',
            'opencv-python',
            'matplotlib',
            'pyyaml',
            'numpy'
        ]
        
        for package in dependencies:
            try:
                print(f"Installing {package}...")
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ {package} installed successfully")
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to install {package}: {e}")
                return False
        
        print("✅ All dependencies installed")
        return True
    
    def test_plant_database(self):
        """Test plant database loading"""
        print("\n🌱 Testing plant database...")
        
        try:
            with open('plant_database.json', 'r', encoding='utf-8') as f:
                plant_db = json.load(f)
            
            plant_count = len(plant_db.get('plant_info', {}))
            print(f"✅ Plant database loaded: {plant_count} plants")
            
            # Test a few entries
            sample_plants = list(plant_db['plant_info'].keys())[:3]
            for plant_id in sample_plants:
                plant = plant_db['plant_info'][plant_id]
                print(f"   - {plant['common_name']} ({plant['scientific_name']})")
            
            return True
            
        except Exception as e:
            print(f"❌ Plant database test failed: {e}")
            return False
    
    def test_model_loading(self):
        """Test YOLO model loading"""
        print("\n🤖 Testing model loading...")
        
        try:
            from ultralytics import YOLO
            
            # Try to load a model
            model_paths = [
                "runs/detect/enhanced_plant_detection/weights/best.pt",
                "runs/detect/train/weights/best.pt",
                "yolov8m.pt"
            ]
            
            model_loaded = False
            for model_path in model_paths:
                try:
                    if os.path.exists(model_path) or model_path.startswith('yolov8'):
                        model = YOLO(model_path)
                        print(f"✅ Model loaded from: {model_path}")
                        model_loaded = True
                        break
                except Exception as e:
                    print(f"⚠️ Failed to load {model_path}: {e}")
                    continue
            
            if not model_loaded:
                print("❌ No model could be loaded")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ Model loading test failed: {e}")
            return False
    
    def test_api_functionality(self):
        """Test the plant detection API"""
        print("\n🔧 Testing API functionality...")
        
        try:
            from plant_detection_api import PlantDetectionAPI
            
            api = PlantDetectionAPI()
            info = api.get_model_info()
            
            print(f"✅ API initialized")
            print(f"   - Model Status: {info['status']}")
            print(f"   - Database Loaded: {info.get('database_loaded', False)}")
            
            return True
            
        except Exception as e:
            print(f"❌ API test failed: {e}")
            return False
    
    def run_training_test(self):
        """Run a quick training test"""
        print("\n🏋️ Running training test...")
        
        try:
            # Import and run enhanced training script
            from enhanced_training_script import PlantDetectionModel
            
            print("Creating model instance...")
            detector = PlantDetectionModel("yolov8n.pt")  # Use nano for faster testing
            
            print("✅ Training system ready")
            print("⚠️ Skipping actual training for quick deployment test")
            print("   Run 'python enhanced_training_script.py' to train the model")
            
            return True
            
        except Exception as e:
            print(f"❌ Training test failed: {e}")
            return False
    
    def test_web_interface(self):
        """Test web interface components"""
        print("\n🌐 Testing web interface...")
        
        try:
            # Check Flask app
            from app import app
            
            print("✅ Flask app imported successfully")
            
            # Check template
            template_path = Path('templates/index.html')
            if template_path.exists():
                print("✅ HTML template found")
            else:
                print("❌ HTML template missing")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ Web interface test failed: {e}")
            return False
    
    def create_startup_script(self):
        """Create startup scripts for the system"""
        print("\n📝 Creating startup scripts...")
        
        # Create training script
        training_script = """#!/bin/bash
echo "🌱 Starting Plant Detection Training"
python enhanced_training_script.py
"""
        
        with open('start_training.sh', 'w') as f:
            f.write(training_script)
        
        # Create web app script
        webapp_script = """#!/bin/bash
echo "🌐 Starting Plant Detection Web App"
echo "Open your browser and go to: http://localhost:5000"
python app.py
"""
        
        with open('start_webapp.sh', 'w') as f:
            f.write(webapp_script)
        
        # Create Windows batch files
        training_bat = """@echo off
echo 🌱 Starting Plant Detection Training
python enhanced_training_script.py
pause
"""
        
        with open('start_training.bat', 'w') as f:
            f.write(training_bat)
        
        webapp_bat = """@echo off
echo 🌐 Starting Plant Detection Web App
echo Open your browser and go to: http://localhost:5000
python app.py
pause
"""
        
        with open('start_webapp.bat', 'w') as f:
            f.write(webapp_bat)
        
        print("✅ Startup scripts created:")
        print("   - start_training.sh / start_training.bat")
        print("   - start_webapp.sh / start_webapp.bat")
    
    def create_readme(self):
        """Create comprehensive README file"""
        print("\n📚 Creating README file...")
        
        readme_content = """# 🌱 Plant Detection System

An advanced plant detection system using YOLOv8 with scientific and local plant names.

## Features

- 🔍 **Plant Detection**: Detect 25 different plant species
- 🧬 **Scientific Names**: Get scientific nomenclature for each plant
- 🌍 **Local Names**: Multiple local/common names for each plant
- 🌐 **Web Interface**: User-friendly web application
- 📊 **Detailed Reports**: Comprehensive detection reports
- 🎯 **High Accuracy**: Trained on specialized plant dataset

## Quick Start

### 1. Install Dependencies
```bash
pip install ultralytics flask pillow opencv-python matplotlib pyyaml numpy
```

### 2. Train the Model (Optional)
```bash
python enhanced_training_script.py
```
Or use the startup script:
- Windows: `start_training.bat`
- Linux/Mac: `./start_training.sh`

### 3. Start Web Application
```bash
python app.py
```
Or use the startup script:
- Windows: `start_webapp.bat`
- Linux/Mac: `./start_webapp.sh`

### 4. Open Browser
Navigate to: http://localhost:5000

## Plant Database

The system can detect and identify:
- Adam Hawa ungu (Ruellia simplex)
- Aglaonema (Chinese Evergreen)
- Anggrek (Orchid)
- Cabai (Chili Pepper)
- Daun bawang (Green Onion)
- Heliconia (Lobster Claw)
- Hydragea (Hydrangea)
- Jengger ayam (Cockscomb)
- Kana (Canna Lily)
- Kembang Sepatu (Chinese Hibiscus)
- Kemuning (Orange Jasmine)
- Lidah Mertua (Snake Plant)
- Marigold (African Marigold)
- Matahari (Sunflower)
- Miana (Coleus)
- Pacar air (Garden Balsam)
- Patrakomala (Purple Passion)
- Pucuk Merah (Red Shoot)
- Puring (Croton)
- Ruellia (Minnieroot)
- Sawi (Mustard Greens)
- Selada (Lettuce)
- Telang (Butterfly Pea)
- Thunbergia (Black-eyed Susan Vine)
- Tomat (Tomato)

## File Structure

```
Plant Detection System/
├── plant_database.json          # Plant information database
├── enhanced_data.yaml           # Enhanced dataset configuration
├── enhanced_training_script.py  # Training script
├── app.py                      # Flask web application
├── plant_detection_api.py      # Detection API backend
├── result_display.py           # Result formatting system
├── templates/
│   └── index.html              # Web interface template
├── train/                      # Training data
├── valid/                      # Validation data
├── test/                       # Test data
└── runs/                       # Training outputs
```

## API Usage

```python
from plant_detection_api import PlantDetectionAPI

# Initialize API
api = PlantDetectionAPI()

# Detect plants in image
detections = api.detect_from_file('path/to/image.jpg')

# Get plant information
for detection in detections:
    plant_info = detection['plant_info']
    print(f"Plant: {plant_info['common_name']}")
    print(f"Scientific: {plant_info['scientific_name']}")
    print(f"Local names: {', '.join(plant_info['local_names'])}")
```

## Troubleshooting

1. **Model not found**: Run training script first or ensure yolov8m.pt is available
2. **Dependencies missing**: Run `pip install -r requirements.txt`
3. **Port 5000 in use**: Change port in app.py or kill existing process
4. **Low accuracy**: Ensure good lighting and clear plant images

## Contributing

1. Add new plants to plant_database.json
2. Update enhanced_data.yaml with new classes
3. Retrain model with new data
4. Test thoroughly before deployment

## License

This project is licensed under the MIT License.
"""
        
        with open('README.md', 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("✅ README.md created")
    
    def deploy_system(self):
        """Deploy the complete system"""
        print("🚀 PLANT DETECTION SYSTEM DEPLOYMENT")
        print("=" * 50)
        
        # Run all tests
        tests = [
            ("System Requirements", self.check_system_requirements),
            ("Dependencies", self.install_dependencies),
            ("Plant Database", self.test_plant_database),
            ("Model Loading", self.test_model_loading),
            ("API Functionality", self.test_api_functionality),
            ("Training System", self.run_training_test),
            ("Web Interface", self.test_web_interface)
        ]
        
        failed_tests = []
        
        for test_name, test_func in tests:
            try:
                if not test_func():
                    failed_tests.append(test_name)
            except Exception as e:
                print(f"❌ {test_name} test crashed: {e}")
                failed_tests.append(test_name)
        
        # Create deployment files
        self.create_startup_script()
        self.create_readme()
        
        # Final report
        print("\n" + "=" * 50)
        print("🎉 DEPLOYMENT COMPLETE")
        print("=" * 50)
        
        if failed_tests:
            print("⚠️ Some tests failed:")
            for test in failed_tests:
                print(f"   - {test}")
            print("\nPlease fix these issues before using the system.")
        else:
            print("✅ All tests passed successfully!")
        
        print("\n📋 Next Steps:")
        print("1. Train the model: python enhanced_training_script.py")
        print("2. Start web app: python app.py")
        print("3. Open browser: http://localhost:5000")
        print("4. Upload plant images and enjoy! 🌱")
        
        return len(failed_tests) == 0

if __name__ == "__main__":
    deployer = PlantDetectionSystemDeployer()
    success = deployer.deploy_system()
    
    if success:
        print("\n🎊 System ready for use!")
    else:
        print("\n⚠️ Please fix the issues above before proceeding.")
        sys.exit(1)
