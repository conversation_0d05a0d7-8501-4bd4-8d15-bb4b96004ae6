#!/usr/bin/env python3
"""
Demo Plant Detection App - Shows correct output format
This version demonstrates the exact output you requested
"""

import os
import json
from flask import Flask, render_template, request, jsonify
from werkzeug.utils import secure_filename
import base64

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024
app.config['UPLOAD_FOLDER'] = 'uploads'

os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Load plant database
plant_db = None
try:
    with open('plant_database.json', 'r', encoding='utf-8') as f:
        plant_db = json.load(f)
    print(f"✅ Plant database loaded: {len(plant_db['plant_info'])} species")
except Exception as e:
    print(f"❌ Database error: {e}")

def get_plant_info(class_id):
    """Get plant information by class ID"""
    if plant_db and str(class_id) in plant_db['plant_info']:
        return plant_db['plant_info'][str(class_id)]
    return None

def allowed_file(filename):
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and demonstrate plant detection output"""
    
    if 'file' not in request.files:
        return jsonify({'error': 'No file uploaded'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if not allowed_file(file.filename):
        return jsonify({'error': 'Invalid file type'}), 400
    
    try:
        # Save uploaded file
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Demonstrate detection with multiple plants from your database
        detections = []
        
        # Detection 1: Adam Hawa ungu
        plant_info_1 = get_plant_info(0)
        if plant_info_1:
            detection_1 = {
                'class_id': 0,
                'confidence': 0.89,
                'bbox': [120, 80, 350, 280],
                'plant_info': plant_info_1
            }
            detections.append(detection_1)
        
        # Detection 2: Aglaonema
        plant_info_2 = get_plant_info(1)
        if plant_info_2:
            detection_2 = {
                'class_id': 1,
                'confidence': 0.76,
                'bbox': [200, 150, 400, 320],
                'plant_info': plant_info_2
            }
            detections.append(detection_2)
        
        # Detection 3: Anggrek (Orchid)
        plant_info_3 = get_plant_info(2)
        if plant_info_3:
            detection_3 = {
                'class_id': 2,
                'confidence': 0.82,
                'bbox': [50, 200, 250, 400],
                'plant_info': plant_info_3
            }
            detections.append(detection_3)
        
        # Convert image to base64
        with open(filepath, 'rb') as img_file:
            img_data = base64.b64encode(img_file.read()).decode('utf-8')
        
        # Clean up
        os.remove(filepath)
        
        return jsonify({
            'success': True,
            'detections': detections,
            'image_data': img_data,
            'filename': filename,
            'total_detections': len(detections),
            'message': 'Demo showing correct plant identification format'
        })
        
    except Exception as e:
        return jsonify({'error': f'Processing failed: {str(e)}'}), 500

@app.route('/health')
def health_check():
    return jsonify({
        'status': 'healthy',
        'database_loaded': plant_db is not None,
        'plant_count': len(plant_db['plant_info']) if plant_db else 0,
        'mode': 'demo'
    })

if __name__ == '__main__':
    print("🌱 PLANT DETECTION DEMO")
    print("=" * 40)
    print("📊 This demo shows the correct output format:")
    print("   ✅ Image name")
    print("   ✅ Local/Common name") 
    print("   ✅ Scientific name")
    print("   ✅ Additional plant information")
    print(f"📚 Database: {len(plant_db['plant_info']) if plant_db else 0} plants loaded")
    print("🌐 Server: http://localhost:5000")
    print("=" * 40)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
