#!/usr/bin/env python3
"""
Run the exact YOLO commands as requested
"""

from ultralytics import YOLO
import os

print("Starting YOLO training and inference...")

# Step 1: Import YOLO (already done above)
print("✓ YOLO imported successfully")

# Step 2: Load model
print("\nLoading YOLOv8m model...")
model = YOLO("yolov8m.pt")
print("✓ Model loaded successfully")

# Step 3: Train the model
print("\nStarting training with data.yaml for 2 epochs...")
try:
    model.train(data="data.yaml", epochs=2)
    print("✓ Training completed successfully")
except Exception as e:
    print(f"❌ Training failed: {e}")
    exit(1)

# Step 4: Load the best trained model for inference
print("\nLoading best trained model for inference...")
try:
    # The path should be in runs/detect/train/weights/best.pt (default YOLO structure)
    best_model_path = "runs/detect/train/weights/best.pt"
    if os.path.exists(best_model_path):
        infer = YOLO(best_model_path)
        print(f"✓ Loaded best model from: {best_model_path}")
    else:
        # Try alternative paths
        alternative_paths = [
            "runs/detect/train2/weights/best.pt",
            "runs/detect/train3/weights/best.pt",
            "runs/detect/train4/weights/best.pt"
        ]
        found = False
        for path in alternative_paths:
            if os.path.exists(path):
                infer = YOLO(path)
                print(f"✓ Loaded best model from: {path}")
                found = True
                break
        
        if not found:
            print("⚠️ Best model not found, using original model")
            infer = model
            
except Exception as e:
    print(f"❌ Error loading inference model: {e}")
    print("Using original model for inference")
    infer = model

# Step 5: Run inference on test images
print("\nRunning inference on test images...")
try:
    test_images_path = "test/images"
    if os.path.exists(test_images_path):
        results = infer.predict(test_images_path, save=True, save_txt=True)
        print("✓ Inference completed successfully")
        print("📁 Results saved in runs/detect/predict/ directory")
    else:
        print(f"❌ Test images directory not found: {test_images_path}")
        
except Exception as e:
    print(f"❌ Inference failed: {e}")

print("\n🎉 All tasks completed!")
