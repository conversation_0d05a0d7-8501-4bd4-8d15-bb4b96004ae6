#!/usr/bin/env python3
"""
Simple test script to check if the app can run
"""

print("🔍 Testing imports...")

try:
    import os
    print("✅ os imported")
    
    import json
    print("✅ json imported")
    
    from flask import Flask
    print("✅ Flask imported")
    
    from ultralytics import YOLO
    print("✅ YOLO imported")
    
    print("\n🌱 Testing plant database...")
    with open('plant_database.json', 'r', encoding='utf-8') as f:
        plant_db = json.load(f)
    print(f"✅ Plant database loaded: {len(plant_db['plant_info'])} plants")
    
    print("\n🤖 Testing YOLO model...")
    model = YOLO("yolov8n.pt")  # Use nano for quick test
    print("✅ YOLO model loaded successfully")
    
    print("\n🌐 Testing Flask app...")
    app = Flask(__name__)
    
    @app.route('/')
    def test():
        return "Plant Detection System is working!"
    
    print("✅ Flask app created successfully")
    print("\n🚀 Starting test server on http://localhost:5001")
    app.run(debug=True, host='0.0.0.0', port=5001)
    
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Error: {e}")
