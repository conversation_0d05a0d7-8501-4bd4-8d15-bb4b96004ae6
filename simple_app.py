#!/usr/bin/env python3
"""
Simplified Plant Detection Web App for Testing
"""

import os
import json
from flask import Flask, render_template, request, jsonify
from werkzeug.utils import secure_filename
import base64

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Load plant database
plant_db = None
try:
    with open('plant_database.json', 'r', encoding='utf-8') as f:
        plant_db = json.load(f)
    print("✅ Plant database loaded successfully")
except Exception as e:
    print(f"❌ Error loading plant database: {e}")

def get_plant_info(class_id):
    """Get plant information by class ID"""
    if plant_db and str(class_id) in plant_db['plant_info']:
        return plant_db['plant_info'][str(class_id)]
    return {
        'common_name': f'Test Plant {class_id}',
        'scientific_name': 'Plantus testicus',
        'local_names': ['Test Plant', 'Sample Plant'],
        'family': 'Testaceae',
        'description': 'This is a test plant for demonstration purposes'
    }

def allowed_file(filename):
    """Check if file extension is allowed"""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and simulate plant detection"""
    if 'file' not in request.files:
        return jsonify({'error': 'No file uploaded'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if not allowed_file(file.filename):
        return jsonify({'error': 'Invalid file type'}), 400
    
    try:
        # Save uploaded file
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Simulate plant detection (for testing without model)
        # In real app, this would use YOLO model
        mock_detections = [
            {
                'class_id': 0,
                'confidence': 0.85,
                'bbox': [100, 100, 300, 300],
                'plant_info': get_plant_info(0)
            },
            {
                'class_id': 9,
                'confidence': 0.72,
                'bbox': [200, 200, 400, 400],
                'plant_info': get_plant_info(9)
            }
        ]
        
        # Convert image to base64 for display
        with open(filepath, 'rb') as img_file:
            img_data = base64.b64encode(img_file.read()).decode('utf-8')
        
        # Clean up uploaded file
        os.remove(filepath)
        
        return jsonify({
            'success': True,
            'detections': mock_detections,
            'image_data': img_data,
            'filename': filename,
            'message': 'This is a test version with mock detections. Real model integration coming next!'
        })
        
    except Exception as e:
        return jsonify({'error': f'Processing failed: {str(e)}'}), 500

@app.route('/plant_info/<int:class_id>')
def plant_info(class_id):
    """Get detailed plant information"""
    info = get_plant_info(class_id)
    return jsonify(info)

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'database_loaded': plant_db is not None,
        'message': 'Simple Plant Detection Test App'
    })

if __name__ == '__main__':
    print("🌱 Starting Simple Plant Detection Test App")
    print("=" * 50)
    print("📱 Open your browser and go to: http://localhost:5000")
    print("🔍 This version uses mock detections for testing")
    print("✅ Upload any plant image to see the interface in action!")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
