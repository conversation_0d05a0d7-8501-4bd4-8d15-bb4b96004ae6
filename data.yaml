train: train/images
val: valid/images
test: test/images

nc: 25
names: ['<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON> bawang', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON> a<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON> Me<PERSON>ua', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>ar air', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON> Merah', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Tel<PERSON>', 'Thunbergia', 'Tomat']

roboflow:
  workspace: radith
  project: plant-detection1
  version: 12
  license: CC BY 4.0
  url: https://universe.roboflow.com/radith/plant-detection1/dataset/12